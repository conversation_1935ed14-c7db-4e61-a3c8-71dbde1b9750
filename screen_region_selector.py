import tkinter as tk
from PIL import Image, ImageTk, ImageGrab
import logging

class ScreenRegionSelector:
    """屏幕區域選擇器 - 允許用戶通過鼠標拖拽選擇屏幕區域"""
    
    def __init__(self, callback=None):
        """
        初始化屏幕區域選擇器
        callback: 選擇完成後的回調函數，接收 (x1, y1, x2, y2) 參數
        """
        self.callback = callback
        self.start_x = None
        self.start_y = None
        self.end_x = None
        self.end_y = None
        self.selection_window = None
        self.canvas = None
        self.rect_id = None
        self.screenshot = None
        
    def start_selection(self):
        """開始屏幕區域選擇"""
        try:
            # 截取整個屏幕
            self.screenshot = ImageGrab.grab()
            
            # 創建全屏透明窗口
            self.selection_window = tk.Toplevel()
            self.selection_window.attributes('-fullscreen', True)
            self.selection_window.attributes('-topmost', True)
            self.selection_window.attributes('-alpha', 0.3)  # 半透明
            self.selection_window.configure(bg='black')
            self.selection_window.overrideredirect(True)  # 無邊框
            
            # 創建畫布
            self.canvas = tk.Canvas(
                self.selection_window,
                highlightthickness=0,
                bg='black'
            )
            self.canvas.pack(fill='both', expand=True)
            
            # 將截圖顯示在畫布上
            screen_width = self.screenshot.width
            screen_height = self.screenshot.height
            
            # 調整截圖大小以適應畫布
            photo = ImageTk.PhotoImage(self.screenshot)
            self.canvas.create_image(0, 0, anchor='nw', image=photo)
            self.canvas.image = photo  # 保持引用
            
            # 綁定鼠標事件
            self.canvas.bind('<Button-1>', self.on_mouse_down)
            self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
            self.canvas.bind('<ButtonRelease-1>', self.on_mouse_up)
            
            # 綁定鍵盤事件
            self.selection_window.bind('<Escape>', self.cancel_selection)
            self.selection_window.focus_set()
            
            # 添加提示文字
            self.canvas.create_text(
                screen_width // 2, 50,
                text="拖拽鼠標選擇區域，ESC取消",
                fill='white',
                font=('Arial', 16, 'bold')
            )
            
            logging.info("屏幕區域選擇器已啟動")
            
        except Exception as e:
            logging.error(f"啟動屏幕區域選擇器失敗: {str(e)}")
            if self.selection_window:
                self.selection_window.destroy()
    
    def on_mouse_down(self, event):
        """鼠標按下事件"""
        self.start_x = event.x
        self.start_y = event.y
        
        # 清除之前的選擇框
        if self.rect_id:
            self.canvas.delete(self.rect_id)
    
    def on_mouse_drag(self, event):
        """鼠標拖拽事件"""
        if self.start_x is not None and self.start_y is not None:
            # 清除之前的選擇框
            if self.rect_id:
                self.canvas.delete(self.rect_id)
            
            # 繪製新的選擇框
            self.rect_id = self.canvas.create_rectangle(
                self.start_x, self.start_y, event.x, event.y,
                outline='red', width=2, fill='', stipple='gray50'
            )
    
    def on_mouse_up(self, event):
        """鼠標釋放事件"""
        self.end_x = event.x
        self.end_y = event.y
        
        # 確保坐標順序正確
        x1 = min(self.start_x, self.end_x)
        y1 = min(self.start_y, self.end_y)
        x2 = max(self.start_x, self.end_x)
        y2 = max(self.start_y, self.end_y)
        
        # 檢查選擇區域是否有效
        if abs(x2 - x1) > 10 and abs(y2 - y1) > 10:
            logging.info(f"選擇區域: ({x1}, {y1}) 到 ({x2}, {y2})")
            
            # 關閉選擇窗口
            self.selection_window.destroy()
            
            # 調用回調函數
            if self.callback:
                self.callback(x1, y1, x2, y2)
        else:
            logging.warning("選擇區域太小，請重新選擇")
    
    def cancel_selection(self, event=None):
        """取消選擇"""
        logging.info("取消屏幕區域選擇")
        if self.selection_window:
            self.selection_window.destroy()


class ScreenMonitor:
    """屏幕監控器 - 監控指定區域的文本變化"""
    
    def __init__(self, region=None, callback=None):
        """
        初始化屏幕監控器
        region: 監控區域 (x1, y1, x2, y2)
        callback: 檢測到新文本時的回調函數
        """
        self.region = region
        self.callback = callback
        self.monitoring = False
        self.last_text = ""
        self.monitor_thread = None
        
        # 配置 pytesseract (如果需要指定 tesseract 路徑)
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    
    def set_region(self, x1, y1, x2, y2):
        """設置監控區域"""
        self.region = (x1, y1, x2, y2)
        logging.info(f"設置監控區域: {self.region}")
    
    def start_monitoring(self, interval=2.0):
        """開始監控"""
        if not self.region:
            logging.error("未設置監控區域")
            return
        
        if self.monitoring:
            logging.warning("監控已在運行中")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logging.info(f"開始監控區域 {self.region}，檢測間隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logging.info("停止監控")
    
    def _monitor_loop(self, interval):
        """監控循環"""
        while self.monitoring:
            try:
                # 截取指定區域
                screenshot = ImageGrab.grab(bbox=self.region)
                
                # 轉換為 OpenCV 格式
                cv_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                
                # 圖像預處理以提高 OCR 準確性
                gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                
                # 應用高斯模糊減少噪聲
                blurred = cv2.GaussianBlur(gray, (5, 5), 0)
                
                # 應用閾值處理
                _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                
                # 使用 OCR 提取文本
                text = pytesseract.image_to_string(
                    thresh,
                    config='--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?:;()[]{}"\'-_@#$%^&*+=<>/\\|`~'
                ).strip()
                
                # 檢查是否有新文本
                if text and text != self.last_text:
                    # 過濾掉太短的文本（可能是噪聲）
                    if len(text) > 5:
                        logging.info(f"檢測到新文本: {text}")
                        self.last_text = text
                        
                        # 調用回調函數
                        if self.callback:
                            self.callback(text)
                
                time.sleep(interval)
                
            except Exception as e:
                logging.error(f"監控過程中發生錯誤: {str(e)}")
                time.sleep(interval)
    
    def get_current_text(self):
        """獲取當前區域的文本（一次性）"""
        if not self.region:
            return ""
        
        try:
            screenshot = ImageGrab.grab(bbox=self.region)
            cv_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            text = pytesseract.image_to_string(
                thresh,
                config='--psm 6'
            ).strip()
            
            return text
        except Exception as e:
            logging.error(f"獲取當前文本失敗: {str(e)}")
            return ""
