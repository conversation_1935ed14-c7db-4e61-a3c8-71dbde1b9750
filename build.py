import sys
import os
import subprocess
import logging
import venv
from pathlib import Path

# 設置日誌
logging.basicConfig(level=logging.DEBUG,
                   format='%(asctime)s - %(levelname)s - %(message)s')

def create_venv():
    """創建虛擬環境"""
    try:
        venv_path = Path("venv311")
        if not venv_path.exists():
            logging.info("創建虛擬環境...")
            # 創建虛擬環境
            venv.create(venv_path, with_pip=True)
            
        # 獲取虛擬環境的 Python 解釋器路徑
        if os.name == 'nt':  # Windows
            python_path = venv_path / "Scripts" / "python.exe"
        else:  # Linux/Mac
            python_path = venv_path / "bin" / "python"
            
        if not python_path.exists():
            logging.error(f"找不到虛擬環境的Python解釋器: {python_path}")
            return None
            
        return str(python_path)
        
    except Exception as e:
        logging.error(f"創建虛擬環境失敗: {str(e)}")
        return None

def check_dependencies(python_path):
    """檢查並安裝必要的依賴"""
    # 使用清華鏡像源
    mirror_url = "https://pypi.tuna.tsinghua.edu.cn/simple"
    
    # 先升級基礎工具
    base_packages = [
        'pip',
        'setuptools',
        'wheel',
        'platformdirs'
    ]
    
    logging.info("升級基礎包...")
    for package in base_packages:
        try:
            subprocess.check_call([
                python_path,
                '-m',
                'pip',
                'install',
                '--upgrade',
                package,
                '-i',
                mirror_url
            ])
        except subprocess.CalledProcessError as e:
            logging.error(f"升級 {package} 失敗: {str(e)}")
            return False

    # 安裝主要依賴
    required_packages = [
        'pyinstaller',  # 打包工具
        'pillow',       # 圖像處理
        'pystray',      # 系統托盤
        'keyboard',     # 鍵盤監聽
        'pyperclip',    # 剪貼板操作
        'openai',       # OpenAI API
        'platformdirs'  # 平台目錄支持
    ]
    
    for package in required_packages:
        try:
            logging.info(f"正在安裝 {package}")
            subprocess.check_call([
                python_path,
                '-m',
                'pip',
                'install',
                package,
                '--no-cache-dir',
                '-i',
                mirror_url,
                '--trusted-host',
                'pypi.tuna.tsinghua.edu.cn'
            ])
            logging.info(f"成功安裝 {package}")
        except subprocess.CalledProcessError as e:
            logging.error(f"安裝 {package} 失敗: {str(e)}")
            return False
    return True

def build_exe(python_path):
    """打包程序"""
    try:
        # 獲取當前腳本所在目錄
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 設置打包參數
        options = [
            'screen_capture.py',
            '--name=AI回應助手',
            '--noconsole',
            '--windowed',
            '--clean',
            '--noconfirm',
            '--onefile',
            f'--distpath={os.path.join(current_dir, "dist")}',
            f'--workpath={os.path.join(current_dir, "build")}',
            '--hidden-import=pystray._win32',
            '--hidden-import=win32api',
            '--hidden-import=win32con',
            '--hidden-import=win32gui',
            '--hidden-import=PIL._tkinter_finder'
        ]
        
        # 如果存在圖標文件，添加圖標參數
        icon_path = os.path.join(current_dir, 'icon.ico')
        if os.path.exists(icon_path):
            options.append(f'--icon={icon_path}')
        
        # 如果存在 README 文件，添加數據文件
        readme_path = os.path.join(current_dir, 'README.md')
        if os.path.exists(readme_path):
            options.append('--add-data=README.md;.')
        
        # 執行打包
        subprocess.check_call([
            python_path,
            '-m',
            'PyInstaller',
            *options
        ])
        logging.info("打包完成")
        return True
        
    except Exception as e:
        logging.error(f"打包失敗: {str(e)}")
        return False

if __name__ == "__main__":
    logging.info("開始創建虛擬環境...")
    python_path = create_venv()
    if python_path:
        logging.info("開始檢查依賴...")
        if check_dependencies(python_path):
            logging.info("開始打包程序...")
            if build_exe(python_path):
                logging.info("程序打包成功！")
            else:
                logging.error("程序打包失敗！")
        else:
            logging.error("依賴安裝失敗，無法繼續打包！")
    else:
        logging.error("創建虛擬環境失敗！") 