import tkinter as tk
from tkinter import ttk
import openai
import pyperclip
import keyboard
import logging

import time
import re
import pystray
from PIL import Image
import threading
import os

# 設置日誌
logging.basicConfig(level=logging.DEBUG,
                   format='%(asctime)s - %(levelname)s - %(message)s')

class ScreenCaptureApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()
        
        # 使用 keyboard 庫註冊全局 F8 快捷鍵
        keyboard.hook(self.check_f8)  # 使用 hook 來捕獲所有按鍵
        
        # 保存原始剪貼板內容
        self.original_clipboard = None
        
        # 創建系統托盤圖標
        self.create_tray_icon()
        
        logging.info("程序初始化完成，等待F8鍵...")

    def create_tray_icon(self):
        """創建系統托盤圖標"""
        try:
            # 嘗試載入外部圖標
            icon_path = os.path.join(os.path.dirname(__file__), 'icon.png')
            if os.path.exists(icon_path):
                image = Image.open(icon_path)
            else:
                # 沒有外部圖標，創建一個簡單的圖標
                image = Image.new('RGBA', (64, 64), (0, 0, 0, 0))  # 創建透明背景
                # 在圖片上繪製一個簡單的圖案
                from PIL import ImageDraw
                draw = ImageDraw.Draw(image)
                # 繪製一個圓形
                draw.ellipse([8, 8, 56, 56], fill='#3498db')  # 藍色圓形
                # 繪製 "AI" 文字
                from PIL import ImageFont
                try:
                    # 嘗試使用系統字體
                    font = ImageFont.truetype("arial.ttf", 24)
                except:
                    # 如果無法載入字體，使用默認字體
                    font = ImageFont.load_default()
                draw.text((20, 20), "AI", fill='white', font=font)
        except Exception as e:
            # 如果創建圖標失敗，創建最簡單的圖標
            logging.warning(f"創建自定義圖標失敗: {str(e)}，使用簡單圖標")
            image = Image.new('RGBA', (64, 64), '#3498db')  # 純藍色方塊
        
        # 創建菜單
        menu = (
            pystray.MenuItem("顯示", self.show_window),
            pystray.MenuItem("退出", self.quit_app)
        )
        
        # 創建系統托盤圖標
        self.icon = pystray.Icon(
            "AI回應助手",
            image,
            "AI回應助手 (F8)",
            menu
        )
        
        # 在新線程中運行圖標
        threading.Thread(target=self.icon.run, daemon=True).start()

    def show_window(self, icon, item):
        """顯示主窗口（如果需要的話）"""
        pass  # 目前不需要實現，因為我們使用 F8 觸發

    def get_selected_text(self):
        """獲取當前選中的文本"""
        MAX_RETRIES = 3
        RETRY_DELAY = 0.1
        
        # 保存原始剪貼板內容
        try:
            self.original_clipboard = pyperclip.paste()
        except Exception as e:
            logging.warning(f"無法獲取原始剪貼板內容: {str(e)}")
            self.original_clipboard = None

        # 模擬 Ctrl+C
        keyboard.send('ctrl+c')
        time.sleep(0.1)  # 等待複製完成
        
        # 獲取選中的文本，使用重試機制
        text = ""
        for attempt in range(MAX_RETRIES):
            try:
                text = pyperclip.paste()
                if text:  # 如果成功獲取文本，跳出循環
                    break
            except Exception as e:
                logging.warning(f"獲取剪貼板內容失敗 (嘗試 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
                if attempt < MAX_RETRIES - 1:  # 如果不是最後一次嘗試
                    time.sleep(RETRY_DELAY)  # 等待一段時間後重試
        
        # 恢復原始剪貼板內容
        if self.original_clipboard:
            for attempt in range(MAX_RETRIES):
                try:
                    pyperclip.copy(self.original_clipboard)
                    break
                except Exception as e:
                    logging.warning(f"恢復剪貼板失敗 (嘗試 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
                    if attempt < MAX_RETRIES - 1:
                        time.sleep(RETRY_DELAY)
        
        return text.strip() if text else ""

    def check_f8(self, event):
        """檢查是否按下 F8"""
        if event.name == 'f8' and event.event_type == 'down':
            self.root.after(0, self.on_f8_press)

    def on_f8_press(self, event=None):
        """當F8按下時獲取選中文本並顯示建議"""
        logging.debug("F8按下事件被觸發")
        
        try:
            # 獲取選中的文本
            text = self.get_selected_text()
            
            if text:
                logging.info(f"獲取到文字: {text}")
                self.show_suggestions(text)
            else:
                logging.warning("未獲取到文字，請先選文字")
        except Exception as e:
            logging.error(f"處理F8按鍵事件時發生錯誤: {str(e)}")
            # 可以選擇在這裡添加錯誤提示給用戶

    def get_ai_suggestions(self, text, has_intent=False):
        """
        獲取 AI 建議
        text: 原文本或帶有回復意願的文本
        has_intent: 是否包含用戶的回復意願
        """
        logging.debug(f"正在請求AI建議，文字內容: {text}")
        try:
            if has_intent:
                system_prompt = """你是一個專業的雙語對話助手。請根據情況提供中英文的回覆建議。

<規則>
1. 請仔細區分「原文」和「回復意願」：
   - 「原文」是用戶要回覆的目標文本
   - 「回復意願」是用戶想要如何回覆，是取代作為AI的你的原本的回復意願，改為使用我的意願標籤內的想法來應對原文的內容。

2. 如果原文是英文：
   - 提供 3 個英文回覆（使用 <EN> 標籤）
   - 提供 3 個中文回覆（使用 <CN> 標籤）
2a. 如果原文是中文：
   - 提供 4 個中文回覆（使用 <CN> 標籤）
   - 提供 2 個英文回覆（使用 <EN> 標籤）


3. 回應原則：
   - 保持專業和理性
   - 提供建設性和關懷的回應
   - 確保中英文回覆都符合對應語言的表達習慣
   - 每個回覆都要有不同的表達風格



5. 嚴格遵守：
   - 必須生成且只能生成 6 個回覆
   - 必須按照規定的語言比例
   - 必須使用正確的標籤和前綴
</規則>"""

                # 修改 messages 的傳遞方式，讓原文和回復意願更清晰
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text},  # text 已經包含了格式化的原文和回復意願
                ]

            else:
                system_prompt = """你是一個專業的雙語對話助手。請生成中英文的對話回覆。

<規則>

1. 如果原文是英文：
   - 提供 3 個英文回覆（使用 <EN> 標籤）
   - 提供 3 個中文回覆（使用 <CN> 標籤）
2. 如果原文是中文：
   - 提供 4 個中文回覆（使用 <CN> 標籤）
   - 提供 2 個英文回覆（使用 <EN> 標籤）

2. 回覆要求：
   - 保持專業和禮貌
   - 提供不同風格的表達
   - 確保中英文回覆都符合對應語言的表達習慣



4. 嚴格遵守：
   - 必須生成且只能生成 6 個回覆
   - 必須按照規定的語言比例
   - 必須使用正確的標籤和前綴
</規則>"""

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text}
                ]
            
            # 記錄完整的請求內容
            logging.debug("發送給 AI 的完整信息")
            logging.debug(f"System prompt: {system_prompt}")
            logging.debug(f"User input: {text}")
            
            # 每次请求都创建新的客户端实例
            client = openai.OpenAI(
                api_key="1234",
                base_url="http://127.0.0.1:4891/v1"
            )
            
            response = client.chat.completions.create(
                model="DeepSeek-R1-Distill-Qwen-14B",
                messages=messages,
                temperature=0,
                top_p=0.5,
                max_tokens=5000,
                stream=False
            )
            
            # 記錄 AI 的原始回應
            raw_response = response.choices[0].message.content
            logging.debug(f"AI 原始回應: {raw_response}")
            
            # 首先移除所有 <think> 標籤及其內容
            cleaned_response = re.sub(r'<think>.*?</think>', '', raw_response, flags=re.DOTALL)
            
            # 提取所有 CN 和 EN 標籤中的內容
            cn_suggestions = re.findall(r'<CN>(.*?)</CN>', cleaned_response, flags=re.DOTALL)
            en_suggestions = re.findall(r'<EN>(.*?)</EN>', cleaned_response, flags=re.DOTALL)
            
            
            
            # 合併所有建議
            suggestions = cn_suggestions + en_suggestions
            
            # 確保不重複
            suggestions = list(dict.fromkeys(suggestions))
            
            logging.info(f"處理後的最終建議 (共 {len(suggestions)} 條): {suggestions}")
            return suggestions
            
        except Exception as e:
            logging.error(f"獲取AI建議失敗: {str(e)}")
            return ["抱歉，暫時無法成建議。"]

    def show_suggestions(self, text):
        # 獲取鼠標當前位置
        mouse_x = self.root.winfo_pointerx()
        mouse_y = self.root.winfo_pointery()
        
        suggestion_window = tk.Toplevel()
        suggestion_window.title("AI建議回復")
        
        # 設置窗口置頂和樣式
        suggestion_window.attributes('-topmost', True)
        suggestion_window.configure(bg='#f0f0f0')
        style = ttk.Style()
        style.configure('Custom.TButton', padding=5)
        
        # 創建主框架
        main_frame = ttk.Frame(suggestion_window, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # 顯示原文
        original_frame = ttk.Frame(main_frame)
        original_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(original_frame, text="原文:", font=('Arial', 10, 'bold')).pack(anchor='w')
        # 限制原文顯示長度
        MAX_DISPLAY_LENGTH = 100  # 最大顯示字符數
        display_text = text[:MAX_DISPLAY_LENGTH] + "..." if len(text) > MAX_DISPLAY_LENGTH else text
        original_text = ttk.Label(original_frame, text=display_text, wraplength=380)
        original_text.pack(fill='x', pady=(5, 0))
        
        # 如果文本被截斷，添加工具提示顯示完整文本
        if len(text) > MAX_DISPLAY_LENGTH:
            def show_full_text(event):
                tooltip = tk.Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.attributes('-topmost', True)
                tooltip.configure(bg='#ffffd0')  # 淺黃色背景
                
                # 計算位置
                x = event.x_root + 10
                y = event.y_root + 10
                
                # 創建文本標籤
                label = tk.Label(tooltip, text=text, wraplength=400, justify='left',
                               bg='#ffffd0', padx=5, pady=5)
                label.pack()
                
                # 調整位置確保完整顯示
                tooltip.update_idletasks()
                if y + tooltip.winfo_height() > tooltip.winfo_screenheight():
                    y = event.y_root - tooltip.winfo_height() - 10
                if x + tooltip.winfo_width() > tooltip.winfo_screenwidth():
                    x = event.x_root - tooltip.winfo_width() - 10
                
                tooltip.geometry(f"+{x}+{y}")
                
                # 滑鼠離開時關閉提示
                def on_leave(e):
                    tooltip.destroy()
                
                tooltip.bind('<Leave>', on_leave)
                label.bind('<Leave>', on_leave)
            
            # 綁定滑鼠懸停事件
            original_text.bind('<Enter>', show_full_text)
        
        # 添加輸入框框架
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill='x', pady=10)
        
        # 創建多行文本輸入框
        input_var = tk.StringVar()
        input_text = tk.Text(input_frame, height=3, wrap=tk.WORD)  # 設置高度為3行，自動換行
        input_text.insert('1.0', "我想回復...")  # 默認提示文字
        input_text.pack(fill='x', expand=True)
        
        # 當輸入框獲得焦點時清除默認文字
        def on_focus_in(event):
            if input_text.get('1.0', 'end-1c') == "我想回復...":
                input_text.delete('1.0', tk.END)
        
        # 當輸入框失去焦點且為空時恢復默認文字
        def on_focus_out(event):
            if not input_text.get('1.0', 'end-1c').strip():
                input_text.delete('1.0', tk.END)
                input_text.insert('1.0', "我想回復...")
        
        input_text.bind('<FocusIn>', on_focus_in)
        input_text.bind('<FocusOut>', on_focus_out)
        
        # 創建按鈕框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(0, 10))
        
        # 確認按鈕放在中間
        confirm_btn = ttk.Button(
            button_frame, 
            text="確認", 
            command=lambda: update_suggestions(input_text.get('1.0', 'end-1c'))
        )
        confirm_btn.pack(pady=5)
        
        # 分隔
        ttk.Separator(main_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # 建議標題
        ttk.Label(main_frame, text="AI 建議回復:", font=('Arial', 10, 'bold')).pack(anchor='w')
        
        # 創建一個帶滾動條的框架
        suggestions_canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=suggestions_canvas.yview)
        suggestions_frame = ttk.Frame(suggestions_canvas)
        
        # 配置滾動
        suggestions_canvas.configure(yscrollcommand=scrollbar.set)
        
        # 打包滾動條和畫布
        scrollbar.pack(side="right", fill="y")
        suggestions_canvas.pack(side="left", fill="both", expand=True)
        
        # 創建窗口來容納建議框架
        canvas_frame = suggestions_canvas.create_window((0, 0), window=suggestions_frame, anchor="nw", width=suggestions_canvas.winfo_width())
        
        def on_frame_configure(event):
            """當框架大小改變時更新滾動區域"""
            suggestions_canvas.configure(scrollregion=suggestions_canvas.bbox("all"))
        
        def on_canvas_configure(event):
            """當畫布大小改變時更新框架寬"""
            width = event.width
            suggestions_canvas.itemconfig(canvas_frame, width=width)
        
        # 綁定事件
        suggestions_frame.bind("<Configure>", on_frame_configure)
        suggestions_canvas.bind("<Configure>", on_canvas_configure)
        
        # 綁定鼠標滾輪
        def on_mousewheel(event):
            suggestions_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        suggestions_canvas.bind_all("<MouseWheel>", on_mousewheel)
        
        # 添加一個標誌來追踪是否已經選擇了建議
        has_selected = False
        
        def update_suggestions(user_input=None):
            # 重置選擇狀態
            nonlocal has_selected
            has_selected = False
            # 取消離開事件綁定
            suggestion_window.unbind('<Leave>')
            
            # 清除現有建議
            for widget in suggestions_frame.winfo_children():
                widget.destroy()
            
            try:
                # 獲取用戶輸入
                if user_input is None:
                    user_input = input_text.get('1.0', 'end-1c')
                
                if user_input == "我想回復...":
                    user_input = ""
                
                if user_input:
                    prompt = f"""請根據以下內容提供回覆建議：

<原文>
{text}
</原文>

<回復意願>
{user_input}
</回復意願>

<回覆規則>
   - 「原文」是用戶要回覆的目標文本
   - 「回復意願」是用戶想要如何回覆，是取代作為AI的你的原本的回復意願，改為使用我的意願標籤內的想法來應對原文的內容。
1. 如果原文是英文：
   - 提供 3 個英文回覆（使用 <EN> 標籤）
   - 提供 3 個中文回覆（使用 <CN> 標籤）
2. 如果原文是中文：
   - 提供 4 個中文回覆（使用 <CN> 標籤）
   - 提供 2 個英文回覆（使用 <EN> 標籤）
3. 回覆格式示例：
   <CN>感謝您的來信...</CN>
   <EN>Thank you for your message...</EN>
4. 每個回覆都要有不同的表達風格
5. 保持專業、禮貌的語氣
</回覆規則>

請嚴格按照上述規則提供回覆。
"""
                else:
                    prompt = f"""請根據以下文本提供回覆建議：

<原文>
{text}
</原文>

<回覆規則>
1. 如果原文是英文：
   - 提供 3 個英文回覆（使用 <EN> 標籤）
   - 提供 3 個中文回覆（使用 <CN> 標籤）
2. 如果原文是中文：
   - 提供 4 個中文回覆（使用 <CN> 標籤）
   - 提供 2 個英文回覆（使用 <EN> 標籤）
3. 回覆格式示例：
   <CN>感謝您的來信...</CN>
   <EN>Thank you for your message...</EN>
4. 每個回覆都要有不同的表達風格
5. 保持專業、禮貌的語氣
</回覆規則>

請嚴格按照上述規則提供回覆。
"""
                suggestions = self.get_ai_suggestions(prompt, has_intent=bool(user_input))
                
                # 顯示建議按鈕
                for i, suggestion in enumerate(suggestions, 1):
                    # 創建按鈕框架
                    btn_frame = ttk.Frame(suggestions_frame)
                    btn_frame.pack(fill='x', pady=5, padx=5)
                    
                    # 創建按鈕
                    button = tk.Button(
                        btn_frame,
                        text=f"{i}. {suggestion}",
                        wraplength=350,  # 設置文字換行寬度
                        justify='left',  # 文字左對齊
                        anchor='w',      # 按鈕內容左對齊
                        relief='groove', # 按鈕邊框樣式
                        bg='#f8f9fa',    # 背景色
                        activebackground='#e9ecef',  # 滑鼠懸停時的背景色
                        padx=10,         # 水平內邊距
                        pady=8,          # 垂直邊距
                    )
                    button.pack(fill='x', expand=True)
                    
                    # 設字體
                    button.configure(font=('Arial', 10))
                    
                    # 綁定點擊事件
                    button.configure(command=lambda s=suggestion: handle_selection(s))
                    
                    # 設鼠標懸停效果
                    def on_enter(e, btn=button):
                        btn.configure(bg='#e9ecef')
                    def on_leave(e, btn=button):
                        btn.configure(bg='#f8f9fa')
                    
                    button.bind('<Enter>', on_enter)
                    button.bind('<Leave>', on_leave)
                
                # 更新滾動區域
                suggestions_frame.update_idletasks()
                suggestions_canvas.configure(scrollregion=suggestions_canvas.bbox("all"))
                suggestions_canvas.yview_moveto(0)
                
            except Exception as e:
                logging.error(f"顯示建議視窗失敗: {str(e)}")
                error_label = ttk.Label(suggestions_frame, text=f"錯誤: {str(e)}", foreground='red')
                error_label.pack(pady=10)
        
        def handle_selection(selected_text):
            """處理選項被選中的事件"""
            nonlocal has_selected
            # 移除語言標記後再複製文本到剪貼板
            cleaned_text = re.sub(r'^\[(EN|中文)\]\s*', '', selected_text)
            pyperclip.copy(cleaned_text)
            logging.info(f"已複製到剪貼板: {cleaned_text}")
            
            # 標記已經選擇
            has_selected = True
            # 開始檢測鼠標離開事件
            suggestion_window.bind('<Leave>', on_leave)
        
        def on_leave(event):
            """處理鼠標離開事件"""
            # 只有在已經選擇了建議且鼠標確實離開窗口時才關閉
            if has_selected and not suggestion_window.winfo_containing(event.x_root, event.y_root):
                suggestion_window.destroy()
        
        # 初始時不綁定 Leave 事件
        suggestion_window.unbind('<Leave>')
        
        # 綁定 Ctrl+Enter 為確認快捷鍵
        def on_ctrl_enter(event):
            update_suggestions()
        
        input_text.bind('<Control-Return>', on_ctrl_enter)
        
        # 初始顯示建議
        update_suggestions()
        
        # ESC 鍵關閉窗口
        suggestion_window.bind('<Escape>', lambda e: suggestion_window.destroy())
        
        # 設置窗口位置
        window_width = 400
        window_height = 600  # 增加默認高度
        screen_width = suggestion_window.winfo_screenwidth()
        screen_height = suggestion_window.winfo_screenheight()
        
        # 計算窗口位置...（保持不變）
        x = min(mouse_x, screen_width - window_width - 10)
        y = min(mouse_y, screen_height - window_height - 10)
        
        if mouse_y > screen_height / 2:
            y = max(10, mouse_y - window_height - 10)
        
        if mouse_x > screen_width / 2:
            x = max(10, mouse_x - window_width - 10)
        
        suggestion_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        suggestion_window.focus_force()

    def copy_to_clipboard_without_close(self, text):
        """複製文本到剪貼板但不關閉窗口"""
        pyperclip.copy(text)
        logging.info(f"已複製到剪貼板: {text}")

    def quit_app(self, icon=None, item=None):
        """退出應用程序"""
        logging.info("程序退出")
        if self.icon:
            self.icon.stop()
        keyboard.unhook_all()
        self.root.quit()

    def run(self):
        logging.info("程序啟動")
        try:
            self.root.mainloop()
        finally:
            # 確保程序退出時清理資源
            self.quit_app()

if __name__ == "__main__":
    app = ScreenCaptureApp()
    app.run() 