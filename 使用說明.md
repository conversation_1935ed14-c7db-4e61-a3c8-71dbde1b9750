# AI回應助手 - Discord自動監控版

## 功能概述

這個增強版的AI回應助手現在支持：

1. **傳統文本選擇回應** (F8) - 選擇文本後按F8獲取AI建議
2. **屏幕區域選擇** (F9) - 選擇Discord對話區域進行監控
3. **自動監控回應** (F10) - 自動檢測新消息並提供AI回應建議

## 使用步驟

### 第一步：啟動程序
```bash
python screen_capture.py
```

程序啟動後會在系統托盤顯示圖標，並等待快捷鍵操作。

### 第二步：選擇監控區域 (F9)
1. 按下 **F9** 鍵
2. 屏幕會變成半透明覆蓋層
3. 用鼠標拖拽選擇Discord對話區域
4. 釋放鼠標完成選擇
5. 按 **ESC** 可取消選擇

### 第三步：開始監控 (F10)
1. 按下 **F10** 鍵開始監控
2. 程序會每1.5秒檢測一次選定區域的文本變化
3. 當檢測到新的Discord消息時，會自動彈出AI回應建議窗口
4. 再次按 **F10** 可停止監控

### 第四步：選擇回應
1. 當檢測到新消息時，會自動顯示AI建議窗口
2. 點擊任意建議將其複製到剪貼板
3. 在Discord中直接粘貼 (Ctrl+V)

## 快捷鍵說明

| 快捷鍵 | 功能 |
|--------|------|
| **F8** | 傳統模式：選擇文本後獲取AI回應建議 |
| **F9** | 選擇屏幕監控區域 |
| **F10** | 開始/停止自動監控 |
| **ESC** | 取消區域選擇 |

## 系統托盤菜單

右鍵點擊系統托盤圖標可以：
- 選擇監控區域 (F9)
- 開始/停止監控 (F10)
- 退出程序

## 注意事項

### OCR識別要求
- 確保Discord字體大小適中（建議12-16px）
- 選擇的區域應該包含完整的對話內容
- 避免選擇包含過多圖標或表情符號的區域

### 性能優化
- 監控間隔設置為1.5秒，平衡了響應速度和系統資源
- 只有檢測到文本變化時才會觸發AI請求
- 過濾掉長度小於5個字符的文本（避免誤觸發）

### 依賴要求
程序需要以下Python庫：
- `tkinter` (GUI界面)
- `openai` (AI API調用)
- `pyperclip` (剪貼板操作)
- `keyboard` (全局快捷鍵)
- `pystray` (系統托盤)
- `PIL/Pillow` (圖像處理)
- `cv2/opencv-python` (圖像處理)
- `numpy` (數值計算)
- `pytesseract` (OCR文字識別)

## 故障排除

### 如果OCR識別不準確：
1. 調整Discord的字體大小
2. 確保選擇的區域背景對比度足夠
3. 重新選擇更精確的監控區域

### 如果程序無響應：
1. 檢查系統托盤是否有程序圖標
2. 重新啟動程序
3. 確保所有依賴庫已正確安裝

### 如果AI回應失敗：
1. 檢查網絡連接
2. 確認AI服務器 (http://127.0.0.1:4891/v1) 正在運行
3. 檢查API密鑰配置

## 技術原理

1. **屏幕截圖**：使用PIL.ImageGrab截取指定區域
2. **圖像預處理**：使用OpenCV進行灰度化、模糊、閾值處理
3. **文字識別**：使用Tesseract OCR提取文本
4. **變化檢測**：比較前後文本內容，檢測新消息
5. **AI處理**：調用本地AI服務生成回應建議
6. **用戶交互**：通過Tkinter顯示建議窗口

## 更新日誌

### v2.0 (當前版本)
- ✅ 新增屏幕區域選擇功能 (F9)
- ✅ 新增自動監控功能 (F10)
- ✅ 集成OCR文字識別
- ✅ 自動檢測Discord新消息
- ✅ 優化用戶界面和交互體驗

### v1.0 (原版本)
- ✅ 基礎文本選擇回應功能 (F8)
- ✅ AI建議生成
- ✅ 系統托盤集成
