import os
import subprocess
import logging
from pathlib import Path

# 設置日誌
logging.basicConfig(level=logging.DEBUG,
                   format='%(asctime)s - %(levelname)s - %(message)s')

def quick_build():
    """快速打包程序"""
    try:
        # 檢查虛擬環境是否存在
        venv_path = Path("venv311")
        if not venv_path.exists():
            logging.error("找不到虛擬環境，請先運行 build.py")
            return False
            
        # 獲取 Python 解釋器路徑
        python_path = venv_path / "Scripts" / "python.exe" if os.name == 'nt' else venv_path / "bin" / "python"
        if not python_path.exists():
            logging.error("找不到虛擬環境的Python解釋器")
            return False

        # 獲取當前腳本所在目錄
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 設置打包參數
        options = [
            'screen_capture.py',
            '--name=AI回應助手',
            '--noconsole',
            '--windowed',
            '--clean',
            '--noconfirm',
            '--onefile',
            f'--distpath={os.path.join(current_dir, "dist")}',
            f'--workpath={os.path.join(current_dir, "build")}',
            '--hidden-import=pystray._win32',
            '--hidden-import=PIL._tkinter_finder'
        ]
        
        # 如果存在圖標文件，添加圖標參數
        icon_path = os.path.join(current_dir, 'icon.ico')
        if os.path.exists(icon_path):
            options.append(f'--icon={icon_path}')
        
        # 如果存在 README 文件，添加數據文件
        readme_path = os.path.join(current_dir, 'README.md')
        if os.path.exists(readme_path):
            options.append('--add-data=README.md;.')
        
        logging.info("開始快速打包...")
        # 執行打包
        subprocess.check_call([
            str(python_path),
            '-m',
            'PyInstaller',
            *options
        ])
        logging.info("打包完成")
        return True
        
    except Exception as e:
        logging.error(f"打包失敗: {str(e)}")
        return False

if __name__ == "__main__":
    if quick_build():
        logging.info("程序打包成功！")
    else:
        logging.error("程序打包失敗！") 